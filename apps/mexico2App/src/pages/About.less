.about-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  
  @media (min-width: 768px) {
    min-height: 667px;
  }
  
  .header {
    background: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .back-btn {
      color: #667eea;
      text-decoration: none;
      font-size: 16px;
      font-weight: 500;
      margin-right: 16px;
    }
    
    h1 {
      font-size: 20px;
      color: #333;
      font-weight: 600;
    }
  }
  
  .content {
    padding: 20px;
    
    .info-card {
      h2 {
        font-size: 18px;
        color: #333;
        margin-bottom: 16px;
        font-weight: 600;
      }
      
      ul {
        list-style: none;
        
        li {
          padding: 8px 0;
          color: #666;
          font-size: 14px;
          position: relative;
          padding-left: 20px;
          
          &:before {
            content: '•';
            color: #667eea;
            font-weight: bold;
            position: absolute;
            left: 0;
          }
        }
      }
    }
  }
}