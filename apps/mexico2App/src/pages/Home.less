.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  @media (min-width: 768px) {
    min-height: 667px;
  }
  
  .header {
    text-align: center;
    padding: 60px 20px 40px;
    color: white;
    
    h1 {
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 8px;
    }
    
    p {
      font-size: 16px;
      opacity: 0.9;
    }
  }
  
  .content {
    padding: 0 20px;
    
    .card {
      text-align: center;
      
      h2 {
        font-size: 24px;
        color: #333;
        margin-bottom: 16px;
      }
      
      p {
        font-size: 16px;
        color: #666;
        line-height: 1.6;
        margin-bottom: 24px;
      }
    }
  }
}