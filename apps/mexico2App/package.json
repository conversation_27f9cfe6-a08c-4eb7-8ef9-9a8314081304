{"name": "@blw/mexico2App", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start:dev": "vite --mode dev", "start:test": "vite --mode test", "start:prod": "vite --mode prod", "build:dev": "vite build --mode dev", "build:test": "vite build --mode test", "build:prod": "vite build --mode prod", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@nativatech/buryTools": "workspace:@nativatech/buryTools@.0.0", "@nativatech/hooks": "workspace:@nativatech/hooks@.0.0", "@nativatech/ui": "workspace:@nativatech/ui@.0.0", "@nativatech/utils": "workspace:@nativatech/utils@.0.0", "clsx": "^2.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.19.0", "vconsole": "^3.15.1"}, "devDependencies": {"@types/node": "^20.9.2", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "less": "^4.2.0", "postcss": "^8.4.31", "postcss-nested": "^6.0.1", "postcss-px-to-viewport": "^1.1.1", "typescript": "^5.2.2", "vite": "^5.0.0"}}