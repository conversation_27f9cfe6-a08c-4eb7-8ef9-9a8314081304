.mobile-container {
  width: 100%;
  min-height: 100vh;
  background: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px 0;

  .mobile-frame {
    width: 100%;
    max-width: 375px;
    min-height: 100vh;
    background: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 0;
    overflow: hidden;
    position: relative;

    @media (min-width: 768px) {
      min-height: 667px;
      border-radius: 20px;
      box-shadow: 0 8px 40px rgba(0, 0, 0, 0.15);
    }
  }

  @media (max-width: 767px) {
    padding: 0;
    background: white;
    
    .mobile-frame {
      box-shadow: none;
      border-radius: 0;
    }
  }
}